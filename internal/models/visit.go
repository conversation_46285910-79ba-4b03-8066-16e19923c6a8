package models

import "time"

type Visit struct {
	ID        string            `bson:"_id,omitempty" json:"id"`
	DoctorID  string            `bson:"doctorId" json:"doctorId"`
	PatientID string            `bson:"patientId" json:"patientId"`
	Notes     string            `bson:"notes" json:"notes"`
	Vitals    map[string]string `bson:"vitals" json:"vitals"`
	CreatedAt time.Time         `bson:"createdAt" json:"createdAt"`
}
