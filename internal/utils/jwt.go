package utils

import (
	"time"

	"github.com/golang-jwt/jwt/v5"
)

var jwtSecret = []byte("SuperSecretKeyChangeMe") // keep in .env

// GenerateJWTToken creates a signed JWT for doctor-patient session
func GenerateJWTToken(doctorID string) (string, error) {
	claims := jwt.MapClaims{
		"doctorId": doctorID,
		"exp":      time.Now().Add(time.Minute * 30).Unix(), // expires in 30 mins
		"iat":      time.Now().Unix(),                       // issued at
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(jwtSecret)
}

// VerifyJWTToken parses and validates a token
func VerifyJWTToken(tokenString string) (*jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Ensure the signing method is HMAC
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, jwt.ErrSignatureInvalid
		}
		return jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		return &claims, nil
	}

	return nil, jwt.ErrSignatureInvalid
}
