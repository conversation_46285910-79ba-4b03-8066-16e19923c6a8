version: "3.8"

services:
  # Medicare Go application
  medicare_app:
    image: keyurbilgi/medicare-server:latest
    container_name: medicare_app
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - DATABASE_URL=*****************************************************************************
      - DBNAME=medicare
      - REDIS_URL=redis://:mysecretpassword@************:30059/0
      - PORT=8081
      - GIN_MODE=release
    network_mode: "host"
    volumes:
      - ./logs:/app/logs # Optional: for application logs
