# Variables
DOCKER_USERNAME ?= keyurbilgi
IMAGE_NAME ?= medicare-server
TAG ?= latest
FULL_IMAGE_NAME = $(DOCKER_USERNAME)/$(IMAGE_NAME):$(TAG)

# Platform targets
PLATFORMS = linux/amd64,linux/arm64

# Default target
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  build-local    - Build image for local platform"
	@echo "  build-multi    - Build multi-platform image (amd64, arm64)"
	@echo "  push           - Build and push multi-platform image to Docker Hub"
	@echo "  setup-buildx   - Setup Docker Buildx builder"
	@echo "  login          - Login to Docker Hub"
	@echo "  run-local      - Run the application locally with docker-compose"
	@echo "  stop-local     - Stop local docker-compose services"
	@echo "  clean          - Remove local images and containers"
	@echo ""
	@echo "Usage examples:"
	@echo "  make push DOCKER_USERNAME=myusername IMAGE_NAME=medicare TAG=v1.0.0"
	@echo "  make build-multi DOCKER_USERNAME=myusername"

# Setup Docker Buildx builder
.PHONY: setup-buildx
setup-buildx:
	@echo "Setting up Docker Buildx builder..."
	docker buildx create --name medicare-builder --use --bootstrap || true
	docker buildx inspect --bootstrap

# Login to Docker Hub
.PHONY: login
login:
	@echo "Logging into Docker Hub..."
	@echo "Please enter your Docker Hub credentials:"
	docker login

# Build image for local platform only
.PHONY: build-local
build-local:
	@echo "Building image for local platform: $(FULL_IMAGE_NAME)"
	docker build -t $(FULL_IMAGE_NAME) .

# Build multi-platform image (doesn't push)
.PHONY: build-multi
build-multi: setup-buildx
	@echo "Building multi-platform image: $(FULL_IMAGE_NAME)"
	@echo "Platforms: $(PLATFORMS)"
	docker buildx build \
		--platform $(PLATFORMS) \
		-t $(FULL_IMAGE_NAME) \
		.

# Build and push multi-platform image to Docker Hub
.PHONY: push
push: setup-buildx
	@echo "Building and pushing multi-platform image: $(FULL_IMAGE_NAME)"
	@echo "Platforms: $(PLATFORMS)"
	docker buildx build \
		--platform $(PLATFORMS) \
		-t $(FULL_IMAGE_NAME) \
		--push \
		.
	@echo "Successfully pushed $(FULL_IMAGE_NAME) to Docker Hub!"

# Build and push with latest and version tags
.PHONY: push-versioned
push-versioned: setup-buildx
	@echo "Building and pushing with version tags..."
	docker buildx build \
		--platform $(PLATFORMS) \
		-t $(DOCKER_USERNAME)/$(IMAGE_NAME):$(TAG) \
		-t $(DOCKER_USERNAME)/$(IMAGE_NAME):latest \
		--push \
		.
	@echo "Successfully pushed versioned images to Docker Hub!"

# Run application locally with docker-compose
.PHONY: run-local
run-local:
	@echo "Starting application with docker-compose..."
	docker-compose up --build -d
	@echo "Application is running at http://localhost:8081"

# Stop local docker-compose services
.PHONY: stop-local
stop-local:
	@echo "Stopping docker-compose services..."
	docker-compose down

# View logs
.PHONY: logs
logs:
	docker-compose logs -f medicare_app

# Clean up local images and containers
.PHONY: clean
clean:
	@echo "Cleaning up local Docker resources..."
	docker-compose down -v --remove-orphans || true
	docker rmi $(FULL_IMAGE_NAME) || true
	docker system prune -f

# Development helpers
.PHONY: dev-build
dev-build:
	@echo "Building development image..."
	docker build -t $(IMAGE_NAME):dev .

.PHONY: dev-run
dev-run: dev-build
	@echo "Running development container..."
	docker run --rm -p 8081:8081 --env-file .env $(IMAGE_NAME):dev

# Show current configuration
.PHONY: config
config:
	@echo "Current configuration:"
	@echo "  Docker Username: $(DOCKER_USERNAME)"
	@echo "  Image Name: $(IMAGE_NAME)"
	@echo "  Tag: $(TAG)"
	@echo "  Full Image Name: $(FULL_IMAGE_NAME)"
	@echo "  Platforms: $(PLATFORMS)"
