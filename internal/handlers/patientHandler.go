package handlers

import (
	"github.com/gin-gonic/gin"
	"github.com/platinumpizza29/medicare/internal/models"
	"github.com/platinumpizza29/medicare/internal/services"
)

type PatientHandler struct {
	patientService *services.PatientService
}

func NewPatientHandler(patientService *services.PatientService) *PatientHandler {
	return &PatientHandler{
		patientService: patientService,
	}
}

type LoginRequest struct {
	Phone    string `json:"phone"`
	Password string `json:"password"`
}

func (s *PatientHandler) LoginPatient(c *gin.Context) {
	var loginRequest *LoginRequest

	if err := c.ShouldBindJSON(&loginRequest); err != nil {
		c.IndentedJSON(400, gin.H{"error": err.Error()})
		return
	}
	// call the service
	ctx := c.Request.Context()
	token, err := s.patientService.GetPatientByPhone(ctx, loginRequest.Phone, loginRequest.Password)
	if err != nil {
		c.IndentedJSON(500, gin.H{"error": err.Error()})
		return
	}

	c.IndentedJSON(200, gin.H{"token": token})
}

func (s *PatientHandler) RegisterPatient(c *gin.Context) {
	var patient *models.Patient

	if err := c.ShouldBindJSON(&patient); err != nil {
		c.IndentedJSON(400, gin.H{"error": err.Error()})
		return
	}
	// call the service
	ctx := c.Request.Context()
	token, err := s.patientService.CreatePatient(ctx, patient)
	if err != nil {
		c.IndentedJSON(500, gin.H{"error": err.Error()})
		return
	}

	c.IndentedJSON(200, gin.H{"token": token})
}

func (s *PatientHandler) GenerateOTP(c *gin.Context) {
	// get the token from headers
	token := c.GetHeader("Authorization")
	if token == "" {
		c.IndentedJSON(401, gin.H{"error": "Unauthorized"})
		return
	}
	phone := c.Query("phone")
	// call the service
	ctx := c.Request.Context()
	otp, err := s.patientService.GenerateOTP(ctx, phone, token)
	if err != nil {
		c.IndentedJSON(500, gin.H{"error": err.Error()})
		return
	}
	c.IndentedJSON(200, gin.H{"otp": otp})
}
