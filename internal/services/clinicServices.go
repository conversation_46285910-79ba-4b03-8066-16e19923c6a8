package services

import (
	"context"

	"github.com/platinumpizza29/medicare/internal/db"
	"github.com/platinumpizza29/medicare/internal/models"
)

type ClinicService struct {
	clinicModel *db.ClinicDB
}

func NewClinicService(clinicDB *db.ClinicDB) *ClinicService {
	return &ClinicService{
		clinicModel: clinicDB,
	}
}

func (s *ClinicService) CreateClinic(ctx context.Context, clinic *models.Clinic) error {
	_, err := s.clinicModel.CreateClinic(ctx, clinic)
	if err != nil {
		return err
	}
	return nil
}
