package db

import (
	"context"
	"time"

	"github.com/platinumpizza29/medicare/internal/models"
	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
)

type DoctorModel struct {
	collection *mongo.Collection
}

func NewDoctorModel(db *mongo.Database) *DoctorModel {
	return &DoctorModel{
		collection: db.Collection("doctors"),
	}
}

// get doctor by phone number
func (s *DoctorModel) GetDoctorByPhone(ctx context.Context, phone string) (*models.Doctor, error) {
	var doctor *models.Doctor
	err := s.collection.FindOne(ctx, bson.M{"phone": phone}).Decode(&doctor)
	if err != nil {
		return nil, err
	}
	return doctor, nil
}

func (s *DoctorModel) CreateDoctor(ctx context.Context, doctor *models.Doctor, db *mongo.Database) (*models.Doctor, error) {
	oid := bson.NewObjectID()
	doctor.ID = oid.Hex()
	doctor.CreatedAt = time.Now()
	doctor.UpdatedAt = time.Now()

	// insert into clinics collection
	_, err := s.collection.InsertOne(ctx, doctor)
	if err != nil {
		return nil, err
	}

	// update the doctor id to the clinic
	_, err = db.Collection("clinics").UpdateOne(ctx,
		bson.M{"_id": doctor.ClinicID},
		bson.M{"$push": bson.M{"doctors": doctor.ID}},
	)
	if err != nil {
		return nil, err
	}

	return doctor, nil
}
