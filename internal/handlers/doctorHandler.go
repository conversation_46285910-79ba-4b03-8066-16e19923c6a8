package handlers

import (
	"github.com/gin-gonic/gin"
	"github.com/platinumpizza29/medicare/internal/models"
	"github.com/platinumpizza29/medicare/internal/services"
)

type DoctorHandler struct {
	doctorService *services.DoctorService
}

func NewDoctorHandler(doctorService *services.DoctorService) *Doctor<PERSON>andler {
	return &DoctorHandler{
		doctorService: doctorService,
	}
}

func (s *Doctor<PERSON>andler) LoginDoctor(c *gin.Context) {
	var doctor *models.Doctor

	if err := c.ShouldBindJSON(&doctor); err != nil {
		c.IndentedJSON(400, gin.H{"error": err.Error()})
		return
	}
	// call the service
	ctx := c.Request.Context()
	token, err := s.doctorService.LoginDoctor(ctx, doctor.Phone, doctor.Password)
	if err != nil {
		c.IndentedJSON(500, gin.H{"error": err.Error()})
		return
	}

	c.IndentedJSON(200, gin.H{"token": token})
}

func (s *<PERSON><PERSON><PERSON><PERSON>) RegisterDoctor(c *gin.Context) {
	var doctor *models.Doctor

	if err := c.Should<PERSON>(&doctor); err != nil {
		c.IndentedJSON(400, gin.H{"error": err.Error()})
		return
	}
	// call the service
	ctx := c.Request.Context()
	if err := s.doctorService.RegisterDoctor(ctx, doctor); err != nil {
		c.IndentedJSON(500, gin.H{"error": err.Error()})
		return
	}

	c.IndentedJSON(200, gin.H{"message": "Doctor created successfully"})
}

type VerifyOTPRequest struct {
	Phone string `json:"phone"`
	OTP   string `json:"otp"`
}

func (s *DoctorHandler) VerifyOTP(c *gin.Context) {
	var req VerifyOTPRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request"})
		return
	}

	ctx := c.Request.Context()
	err := s.doctorService.VerifyOTP(ctx, req.Phone, req.OTP)
	if err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	c.JSON(200, gin.H{"message": "OTP verified successfully"})
}