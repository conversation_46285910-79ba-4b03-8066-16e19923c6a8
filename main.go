package main

import (
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/platinumpizza29/medicare/internal/db"
	"github.com/platinumpizza29/medicare/internal/handlers"
	"github.com/platinumpizza29/medicare/internal/services"
)

func main() {
	// Load environment variables from .env file
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: Error loading .env file: %v", err)
	}

	router := gin.Default()

	mongoUri := os.Getenv("DATABASE_URL")
	dbName := os.Getenv("DBNAME")
	redisUri := os.Getenv("REDIS_URL")

	if err := db.ConnectMongo(mongoUri, dbName); err != nil {
		log.Fatal(err)
	}

	if err := db.ConnectRedis(redisUri); err != nil {
		log.Fatal(err)
	}

	// call db -> services -> handlers
	clinicDB := db.NewClinicDB(db.Database)
	clinicService := services.NewClinicService(clinicDB)
	clinicHandler := handlers.NewClinicHandler(clinicService)

	doctorDB := db.NewDoctorModel(db.Database)
	doctorService := services.NewDoctorService(doctorDB)
	doctorHandler := handlers.NewDoctorHandler(doctorService)

	clinic := router.Group("/clinic")
	{
		clinic.POST("/register", clinicHandler.RegisterClinic)
		clinic.POST("/login", clinicHandler.LoginClinic)
	}

	doctor := router.Group("/doctor")
	{
		doctor.POST("/login", doctorHandler.LoginDoctor)
		doctor.POST("/register", doctorHandler.RegisterDoctor)
	}

	log.Fatal(router.Run(":8081"))
}
