package db

import (
	"context"
	"time"

	"github.com/platinumpizza29/medicare/internal/models"
	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
)

type PatientModel struct {
	collection *mongo.Collection
}

func NewPatientModel(db *mongo.Database) *PatientModel {
	return &PatientModel{
		collection: db.Collection("patients"),
	}
}

// CreatePatient inserts a new patient
func (s *PatientModel) CreatePatient(ctx context.Context, patient *models.Patient) (*models.Patient, error) {
	oid := bson.NewObjectID()
	patient.ID = oid.Hex()
	patient.CreatedAt = time.Now()
	patient.UpdatedAt = time.Now()

	_, err := s.collection.InsertOne(ctx, patient)
	if err != nil {
		return nil, err
	}
	return patient, nil
}

// GetPatientByPhone finds a patient by phone number
func (s *PatientModel) GetPatientByPhone(ctx context.Context, phone string) (*models.Patient, error) {
	var patient *models.Patient
	err := s.collection.FindOne(ctx, bson.M{"phone": phone}).Decode(&patient)
	if err != nil {
		return nil, err
	}
	return patient, nil
}

// GetPatientByID finds a patient by string ID
func (s *PatientModel) GetPatientByID(ctx context.Context, id string) (*models.Patient, error) {
	var patient *models.Patient
	err := s.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&patient)
	if err != nil {
		return nil, err
	}
	return patient, nil
}

// ListPatients fetches all patients
func (s *PatientModel) ListPatients(ctx context.Context) ([]models.Patient, error) {
	cursor, err := s.collection.Find(ctx, bson.M{})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var patients []models.Patient
	if err = cursor.All(ctx, &patients); err != nil {
		return nil, err
	}
	return patients, nil
}

// UpdatePatient updates patient info
func (s *PatientModel) UpdatePatient(ctx context.Context, id string, update bson.M) error {
	update["updatedAt"] = time.Now()

	_, err := s.collection.UpdateOne(ctx,
		bson.M{"_id": id},
		bson.M{"$set": update},
	)
	return err
}

// DeletePatient deletes a patient
func (s *PatientModel) DeletePatient(ctx context.Context, id string) error {
	_, err := s.collection.DeleteOne(ctx, bson.M{"_id": id})
	return err
}
