package db

import (
	"context"
	"time"

	"github.com/platinumpizza29/medicare/internal/models"
	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
)

type ClinicDB struct {
	collection *mongo.Collection
}

func NewClinicDB(db *mongo.Database) *ClinicDB {
	return &ClinicDB{
		collection: db.Collection("clinics"),
	}
}

// CreateClinic inserts a new clinic
func (s *ClinicDB) CreateClinic(ctx context.Context, clinic *models.Clinic) (*models.Clinic, error) {
	oid := bson.NewObjectID()
	clinic.ID = oid.Hex()
	clinic.CreatedAt = time.Now()
	clinic.UpdatedAt = time.Now()

	_, err := s.collection.InsertOne(ctx, clinic)
	if err != nil {
		return nil, err
	}
	return clinic, nil
}

// GetClinicByID finds a clinic by string ID
func (s *ClinicDB) GetClinicByID(ctx context.Context, id string) (*models.Clinic, error) {
	var clinic *models.Clinic
	err := s.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&clinic)
	if err != nil {
		return nil, err
	}
	return clinic, nil
}

// ListClinics fetches all clinics
func (s *ClinicDB) ListClinics(ctx context.Context) ([]models.Clinic, error) {
	cursor, err := s.collection.Find(ctx, bson.M{})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var clinics []models.Clinic
	if err = cursor.All(ctx, &clinics); err != nil {
		return nil, err
	}
	return clinics, nil
}

// UpdateClinic updates clinic info
func (s *ClinicDB) UpdateClinic(ctx context.Context, id string, update bson.M) error {
	update["updatedAt"] = time.Now()

	_, err := s.collection.UpdateOne(ctx,
		bson.M{"_id": id},
		bson.M{"$set": update},
	)
	return err
}

// DeleteClinic deletes a clinic
func (s *ClinicDB) DeleteClinic(ctx context.Context, id string) error {
	_, err := s.collection.DeleteOne(ctx, bson.M{"_id": id})
	return err
}
