package handlers

import (
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/platinumpizza29/medicare/internal/models"
	"github.com/platinumpizza29/medicare/internal/services"
)

type PatientHandler struct {
	patientService *services.PatientService
}

func NewPatientHandler(patientService *services.PatientService) *PatientHandler {
	return &PatientHandler{
		patientService: patientService,
	}
}

type LoginRequest struct {
	Phone    string `json:"phone"`
	Password string `json:"password"`
}

func (s *PatientHandler) LoginPatient(c *gin.Context) {
	var loginRequest *LoginRequest

	if err := c.ShouldBindJSON(&loginRequest); err != nil {
		c.IndentedJSON(400, gin.H{"error": err.Error()})
		return
	}
	// call the service
	ctx := c.Request.Context()
	token, err := s.patientService.GetPatientByPhone(ctx, loginRequest.Phone, loginRequest.Password)
	if err != nil {
		c.IndentedJSON(500, gin.H{"error": err.Error()})
		return
	}

	c.IndentedJSON(200, gin.H{"token": token})
}

func (s *PatientHandler) RegisterPatient(c *gin.Context) {
	var patient *models.Patient

	if err := c.ShouldBindJSON(&patient); err != nil {
		c.IndentedJSON(400, gin.H{"error": err.Error()})
		return
	}
	// call the service
	ctx := c.Request.Context()
	token, err := s.patientService.CreatePatient(ctx, patient)
	if err != nil {
		c.IndentedJSON(500, gin.H{"error": err.Error()})
		return
	}

	c.IndentedJSON(200, gin.H{"token": token})
}

func (s *PatientHandler) GenerateOTP(c *gin.Context) {
	// get the token from headers
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.IndentedJSON(401, gin.H{"error": "Unauthorized"})
		return
	}

	// Extract token from "Bearer <token>" format
	var token string
	if strings.HasPrefix(authHeader, "Bearer ") {
		token = strings.TrimPrefix(authHeader, "Bearer ")
	} else {
		// If no Bearer prefix, use the header value as is
		token = authHeader
	}

	if token == "" {
		c.IndentedJSON(401, gin.H{"error": "Invalid token format"})
		return
	}

	phone := c.Query("phone")
	if phone == "" {
		c.IndentedJSON(400, gin.H{"error": "Phone number is required"})
		return
	}

	// call the service
	ctx := c.Request.Context()
	otp, err := s.patientService.GenerateOTP(ctx, phone, token)
	if err != nil {
		c.IndentedJSON(500, gin.H{"error": err.Error()})
		return
	}
	c.IndentedJSON(200, gin.H{"otp": otp})
}
