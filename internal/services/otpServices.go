package services

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"github.com/redis/go-redis/v9"
)

// generate 6-digit OTP
func GenerateOTP() string {
	rand.Seed(time.Now().UnixNano())
	return fmt.Sprintf("%06d", rand.Intn(1000000))
}

// store OTP in Redis
func StoreOTP(rdb *redis.Client, phone, otp string, ctx context.Context) error {
	return rdb.Set(ctx, "otp:"+phone, otp, 5*time.Minute).Err()
}

// verify OTP from Redis
func VerifyOTP(rdb *redis.Client, phone, otp string, ctx context.Context) bool {
	stored, err := rdb.Get(ctx, "otp:"+phone).Result()
	if err != nil {
		return false
	}
	return stored == otp
}
