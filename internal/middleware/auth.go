package middleware

import (
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/platinumpizza29/medicare/internal/utils"
)

func DoctorAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.AbortWithStatusJSON(401, gin.H{"error": "Unauthorized"})
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.AbortWithStatusJSON(401, gin.H{"error": "Invalid token format"})
			return
		}

		claims, err := utils.VerifyJWTToken(tokenString)
		if err != nil {
			c.AbortWithStatusJSON(401, gin.H{"error": "Invalid token"})
			return
		}

		if _, ok := (*claims)["doctorId"]; !ok {
			c.AbortWithStatusJSON(401, gin.H{"error": "Unauthorized, not a doctor token"})
			return
		}

		c.Next()
	}
}
