package services

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"github.com/platinumpizza29/medicare/internal/db"
	"github.com/platinumpizza29/medicare/internal/models"
	"github.com/platinumpizza29/medicare/internal/utils"
)

type PatientService struct {
	patientModel *db.PatientModel
}

func NewPatientService(patientDB *db.PatientModel) *PatientService {
	return &PatientService{
		patientModel: patientDB,
	}
}

func (s *PatientService) CreatePatient(ctx context.Context, patient *models.Patient) (string, error) {
	encryptedPassword, err := utils.HashPassword(patient.Password)
	if err != nil {
		return "", err
	}
	patient.Password = encryptedPassword

	// create a jwt token
	token, err := utils.GenerateJWTTokenForPatient(patient.ID)
	if err != nil {
		return "", err
	}

	_, err = s.patientModel.CreatePatient(ctx, patient)
	if err != nil {
		return "", err
	}
	return token, nil
}

func (s *PatientService) GetPatientByPhone(ctx context.Context, phone string, password string) (string, error) {
	// check if the password is correct
	cursor, err := s.patientModel.GetPatientByPhone(ctx, phone)
	if err != nil {
		return "", err
	}

	isValid := utils.CheckPasswordHash(password, cursor.Password)
	if !isValid {
		return "", err
	}
	token, err := utils.GenerateJWTTokenForPatient(cursor.ID)
	if err != nil {
		return "", err
	}
	return token, nil
}

func (s *PatientService) GenerateOTP(ctx context.Context, phone string, token string) (string, error) {
	// verify the token
	_, err := utils.VerifyJWTToken(token)
	if err != nil {
		return "", err
	}

	// generate otp
	otp := GenerateNewOTP()
	// store otp in redis
	err = db.StoreOTP(db.RedisClient, phone, otp, ctx)
	if err != nil {
		return "", err
	}
	// return otp
	return otp, nil
}

// generate 6-digit OTP
func GenerateNewOTP() string {
	rand.Seed(time.Now().UnixNano())
	return fmt.Sprintf("%06d", rand.Intn(1000000))
}
