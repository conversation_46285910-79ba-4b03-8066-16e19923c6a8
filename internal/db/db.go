package db

import (
	"context"
	"log"
	"time"

	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
)

var (
	Client      *mongo.Client
	Database    *mongo.Database
	Collection  *mongo.Collection
	RedisClient *redis.Client
)

// ConnectDB establishes a connection to MongoDB
func ConnectMongo(uri string, dbName string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Set client options
	clientOptions := options.Client().ApplyURI(uri)

	// Connect to MongoDB
	client, err := mongo.Connect(clientOptions)
	if err != nil {
		log.Printf("Failed to connect to MongoDB: %v", err)
		return err
	}

	// Ping the database
	err = client.Ping(ctx, nil)
	if err != nil {
		log.Printf("Failed to ping MongoDB: %v", err)
		return err
	}

	// Set global variables
	Client = client
	Database = client.Database(dbName)

	log.Println("Connected to MongoDB!")
	return nil
}

func ConnectRedis(uri string) error {
	opt, err := redis.ParseURL(uri)
	if err != nil {
		log.Printf("Invalid redis url %v", err)
		return err
	}

	rdb := redis.NewClient(opt)

	// test connection
	ctx := context.Background()
	if err := rdb.Ping(ctx).Err(); err != nil {
		log.Printf("Error connecting to redis %v", err)
		return err
	}

	RedisClient = rdb
	log.Printf("Connected to Redis")
	return nil
}
