package handlers

import (
	"github.com/gin-gonic/gin"
	"github.com/platinumpizza29/medicare/internal/models"
	"github.com/platinumpizza29/medicare/internal/services"
)

type PatientHandler struct {
	patientService *services.PatientService
}

func NewPatientHandler(patientService *services.PatientService) *PatientHandler {
	return &PatientHandler{
		patientService: patientService,
	}
}

func (s *PatientHandler) LoginPatient(c *gin.Context) {
	var patient *models.Patient

	if err := c.ShouldBindJSON(&patient); err != nil {
		c.IndentedJSON(400, gin.H{"error": err.Error()})
		return
	}
	// call the service
	ctx := c.Request.Context()
	token, err := s.patientService.GetPatientByPhone(ctx, patient.Phone, patient.Password)
	if err != nil {
		c.IndentedJSON(500, gin.H{"error": err.Error()})
		return
	}

	c.IndentedJSON(200, gin.H{"token": token})
}

func (s *PatientHandler) RegisterPatient(c *gin.Context) {
	var patient *models.Patient

	if err := c.ShouldBindJSON(&patient); err != nil {
		c.IndentedJSON(400, gin.H{"error": err.Error()})
		return
	}
	// call the service
	ctx := c.Request.Context()
	token, err := s.patientService.CreatePatient(ctx, patient)
	if err != nil {
		c.IndentedJSON(500, gin.H{"error": err.Error()})
		return
	}

	c.IndentedJSON(200, gin.H{"token": token})
}
