package models

import "time"

type Clinic struct {
	ID        string    `bson:"_id,omitempty" json:"id"`
	Name      string    `bson:"name" json:"name"`
	Address   string    `bson:"address" json:"address"`
	Phone     string    `bson:"phone" json:"phone"`
	Doctors   []string  `bson:"doctors" json:"doctors"`
	CreatedAt time.Time `bson:"createdAt" json:"createdAt"`
	UpdatedAt time.Time `bson:"updatedAt" json:"updatedAt"`
}
